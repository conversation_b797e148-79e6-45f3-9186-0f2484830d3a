import {expect} from '@loopback/testlab';
import {UserProfileAssignSrfEntityUserController} from '../../../controllers';

describe('UserProfileAssignSrfEntityUserController', () => {
  let controller: any;

  beforeEach(() => {
    // Create simple mocks
    const mockUserProfileRepository = {
      find: () => Promise.resolve([{
        id: 1,
        userId: 'user123',
        supplierCode: 'SUP001',
        information: {
          supplierName: 'Test Supplier',
          companyname: 'Test Company'
        }
      }]),
      assignSrfEntityUsers: () => ({
        create: () => Promise.resolve({})
      })
    };

    const mockAssignSrfEntityUserRepository = {
      updateById: () => Promise.resolve()
    };

    const mockAssignSrfEntityRepository = {
      findOne: () => Promise.resolve(null),
      create: () => Promise.resolve({id: 1})
    };

    const mockUserRepository = {
      findById: () => Promise.resolve({
        id: 'user123',
        email: '<EMAIL>'
      })
    };

    const mockVendorCodeRepository = {
      find: () => Promise.resolve([{
        id: 1,
        code: 'SUP001',
        supplierName: 'Test Supplier',
        supplierEmail2: '<EMAIL>',
        supplierEmail3: '<EMAIL>',
        userProfileId: 1
      }])
    };

    const mockSqsService = {
      sendEmail: () => Promise.resolve({MessageId: 'test-message-id'})
    };

    controller = new UserProfileAssignSrfEntityUserController(
      mockUserProfileRepository as any,
      mockAssignSrfEntityUserRepository as any,
      mockAssignSrfEntityRepository as any,
      mockUserRepository as any,
      mockVendorCodeRepository as any,
      mockSqsService as any
    );
  });

  describe('sendLCAAssignmentEmail', () => {
    it('should send email to multiple recipients when srfId is 86', async () => {
      const mockCreatedRecords = [{
        id: 1,
        srfId: 86,
        reporter_ids: [1],
        userProfileId: 1
      }];

      let emailSent = false;
      let emailSubject = '';
      let emailBody = '';
      let emailTo: any = [];

      // Override the sqsService mock for this test
      controller.sqsService = {
        sendEmail: (to: any, subject: string, body: string, cc: string[]) => {
          emailSent = true;
          emailTo = to;
          emailSubject = subject;
          emailBody = body;
          return Promise.resolve({MessageId: 'test-message-id'});
        }
      };

      // Call the private method using bracket notation
      await controller.sendLCAAssignmentEmail(mockCreatedRecords);

      // Assertions
      expect(emailSent).to.be.true();
      expect(Array.isArray(emailTo)).to.be.true();
      expect(emailTo).to.containEql('<EMAIL>');
      expect(emailTo).to.containEql('<EMAIL>');
      expect(emailTo).to.containEql('<EMAIL>');
      expect(emailSubject).to.match(/Life Cycle Assessment Data Collection Form Assignment/);
      expect(emailSubject).to.match(/Test Supplier/);
      expect(emailSubject).to.match(/SUP001/);
      expect(emailBody).to.match(/Dear Test Supplier/);
      expect(emailBody).to.match(/https:\/\/tvsmotor-supplier\.eisqr\.com\//);
      expect(emailBody).to.match(/TVS Motor Company Limited/);
      expect(emailBody).to.match(/NAVIGOS/);
    });

    it('should not send email when srfId is not 86', async () => {
      const mockCreatedRecords = [{
        id: 1,
        srfId: 85, // Different srfId
        reporter_ids: [1],
        userProfileId: 1
      }];

      let emailSent = false;
      controller.sqsService = {
        sendEmail: () => {
          emailSent = true;
          return Promise.resolve({MessageId: 'test-message-id'});
        }
      };

      // Call the private method
      await controller.sendLCAAssignmentEmail(mockCreatedRecords);

      // Should not send email
      expect(emailSent).to.be.false();
    });

    it('should not send email when reporter_ids is empty', async () => {
      const mockCreatedRecords = [{
        id: 1,
        srfId: 86,
        reporter_ids: [], // Empty reporter_ids
        userProfileId: 1
      }];

      let emailSent = false;
      controller.sqsService = {
        sendEmail: () => {
          emailSent = true;
          return Promise.resolve({MessageId: 'test-message-id'});
        }
      };

      // Call the private method
      await controller.sendLCAAssignmentEmail(mockCreatedRecords);

      // Should not send email
      expect(emailSent).to.be.false();
    });

    it('should filter out invalid emails', async () => {
      // Override vendor repository to return invalid emails
      controller.vendorCodeRepository = {
        find: () => Promise.resolve([{
          id: 1,
          code: 'SUP001',
          supplierName: 'Test Supplier',
          supplierEmail2: 'invalid-email', // Invalid email
          supplierEmail3: '', // Empty email
          userProfileId: 1
        }])
      };

      const mockCreatedRecords = [{
        id: 1,
        srfId: 86,
        reporter_ids: [1],
        userProfileId: 1
      }];

      let emailTo: any = [];
      controller.sqsService = {
        sendEmail: (to: any, subject: string, body: string, cc: string[]) => {
          emailTo = to;
          return Promise.resolve({MessageId: 'test-message-id'});
        }
      };

      await controller.sendLCAAssignmentEmail(mockCreatedRecords);

      // Should only contain the primary SPOC email, not the invalid ones
      expect(emailTo).to.eql(['<EMAIL>']);
    });
  });

  describe('helper functions', () => {
    it('should validate email addresses correctly', () => {
      expect(controller.isValidEmail('<EMAIL>')).to.be.true();
      expect(controller.isValidEmail('invalid-email')).to.be.false();
      expect(controller.isValidEmail('')).to.be.false();
      expect(controller.isValidEmail(null)).to.be.false();
      expect(controller.isValidEmail(undefined)).to.be.false();
    });

    it('should extract unique valid emails from vendor data', () => {
      const vendorData = [
        {
          code: 'SUP001',
          supplierEmail2: '<EMAIL>',
          supplierEmail3: '<EMAIL>'
        },
        {
          code: 'SUP002',
          supplierEmail2: '<EMAIL>', // Duplicate
          supplierEmail3: '<EMAIL>'
        }
      ];

      const result = controller.getUniqueValidEmails(vendorData);

      expect(result).to.have.length(2);
      expect(result[0].emails).to.containEql('<EMAIL>');
      expect(result[0].emails).to.containEql('<EMAIL>');
      expect(result[1].emails).to.containEql('<EMAIL>');
      expect(result[1].emails).to.not.containEql('<EMAIL>'); // Should not contain duplicate
    });
  });
});
